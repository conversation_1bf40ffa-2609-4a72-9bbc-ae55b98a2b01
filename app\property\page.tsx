"use client"

import styles from "./page.module.scss";
import { useEffect, useState, useCallback } from "react";
import JC_Form from "../components/JC_Form/JC_Form";
import { PropertyModel } from "../models/Property";
import { O_BuildingTypeModel } from "../models/O_BuildingType";
import { O_OrientationModel } from "../models/O_Orientation";
import { O_NumBedroomsModel } from "../models/O_NumBedrooms";
import { O_StoreysModel } from "../models/O_Storeys";
import { O_FurnishedModel } from "../models/O_Furnished";
import { O_OccupiedModel } from "../models/O_Occupied";
import { FieldTypeEnum } from "../enums/FieldType";
import { JC_Utils } from "../Utils";
import { JC_FieldOption } from "../models/ComponentModels/JC_FieldOption";

export default function PropertyPage() {

    // - STATE - //

    const [currentProperty, setCurrentProperty] = useState<PropertyModel>(new PropertyModel());
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [rerenderGuid, setRerenderGuid] = useState<string>(JC_Utils.generateGuid());

    // Option lists
    const [buildingTypeOptions, setBuildingTypeOptions] = useState<JC_FieldOption[]>([]);
    const [orientationOptions, setOrientationOptions] = useState<JC_FieldOption[]>([]);
    const [numBedroomsOptions, setNumBedroomsOptions] = useState<JC_FieldOption[]>([]);
    const [storeysOptions, setStoreysOptions] = useState<JC_FieldOption[]>([]);
    const [furnishedOptions, setFurnishedOptions] = useState<JC_FieldOption[]>([]);
    const [occupiedOptions, setOccupiedOptions] = useState<JC_FieldOption[]>([]);

    // Form fields
    const [address, setAddress] = useState<string>("");
    const [buildingTypeCode, setBuildingTypeCode] = useState<string>("");
    const [companyStrataTitle, setCompanyStrataTitle] = useState<string>("");
    const [numBedroomsCode, setNumBedroomsCode] = useState<string>("");
    const [orientationCode, setOrientationCode] = useState<string>("");
    const [storeysCode, setStoreysCode] = useState<string>("");
    const [furnishedCode, setFurnishedCode] = useState<string>("");
    const [occupiedCode, setOccupiedCode] = useState<string>("");


    // Utility function to convert models to field options
    const convertToFieldOptions = (models: any[]) => {
        return models.map(option => ({
            OptionId: option.Code,
            Label: option.Name,
            Selected: false
        })); // Removed sorting to preserve backend SortOrder
    }

    // - EFFECTS - //

    const populateFormFromProperty = useCallback((property: PropertyModel) => {
        setAddress(property.Address || "");
        setBuildingTypeCode(property.BuildingTypeCode || "");
        setCompanyStrataTitle(property.CompanyStrataTitleCode || "");
        setNumBedroomsCode(property.NumBedroomsCode || "");
        setOrientationCode(property.OrientationCode || "");
        setStoreysCode(property.StoreysCode || "");
        setFurnishedCode(property.FurnishedCode || "");
        setOccupiedCode(property.OccupiedCode || "");
    }, []);

    const loadProperty = useCallback(async () => {
        // For now, create a new property. In a real app, you might load an existing one
        const newProperty = new PropertyModel();
        setCurrentProperty(newProperty);
        populateFormFromProperty(newProperty);
    }, [populateFormFromProperty]);

    useEffect(() => {
        loadOptions();
        loadProperty();
    }, [loadProperty]);


    // - LOAD DATA - //

    async function loadOptions() {
        try {
            setIsLoading(true);

            // Load all option lists
            const [
                buildingTypes,
                orientations,
                numBedrooms,
                storeys,
                furnished,
                occupied,
            ] = await Promise.all([
                O_BuildingTypeModel.GetList(),
                O_OrientationModel.GetList(),
                O_NumBedroomsModel.GetList(),
                O_StoreysModel.GetList(),
                O_FurnishedModel.GetList(),
                O_OccupiedModel.GetList(),
            ]);

            // Convert to field options
            setBuildingTypeOptions(convertToFieldOptions(buildingTypes));
            setOrientationOptions(convertToFieldOptions(orientations));
            setNumBedroomsOptions(convertToFieldOptions(numBedrooms));
            setStoreysOptions(convertToFieldOptions(storeys));
            setFurnishedOptions(convertToFieldOptions(furnished));
            setOccupiedOptions(convertToFieldOptions(occupied));

        } catch (error) {
            console.error('Error loading options:', error);
        } finally {
            setIsLoading(false);
        }
    }

    // - FORM HANDLING - //

    async function handleSubmit() {
        try {
            setIsLoading(true);

            const updatedProperty = new PropertyModel({
                ...currentProperty,
                Address: address,
                BuildingTypeCode: buildingTypeCode || undefined,
                CompanyStrataTitleCode: companyStrataTitle || undefined,
                NumBedroomsCode: numBedroomsCode || undefined,
                OrientationCode: orientationCode || undefined,
                StoreysCode: storeysCode || undefined,
                FurnishedCode: furnishedCode || undefined,
                OccupiedCode: occupiedCode || undefined
            });

            let response;
            if (JC_Utils.stringNullOrEmpty(currentProperty.Id)) {
                // Create new property
                response = await PropertyModel.Create(updatedProperty);
            } else {
                // Update existing property
                response = await PropertyModel.Update(updatedProperty);
            }

            if (response) {
                setCurrentProperty(updatedProperty);
                setRerenderGuid(JC_Utils.generateGuid());
            }
        } catch (error) {
            console.error('Error saving property:', error);
        } finally {
            setIsLoading(false);
        }
    }


    // - RENDER - //

    return (
        <div className={styles.mainContainer}>
            {/* Left Pane - Property Form */}
            <div className={styles.leftPane}>
                <div className={styles.leftPaneHeader}>
                    Property Details
                </div>
                <div className={styles.formContainer}>
                    <div className={styles.formTitle}>
                        {JC_Utils.stringNullOrEmpty(currentProperty.Id) ? "New Property" : "Edit Property"}
                    </div>
                    <JC_Form
                        key={rerenderGuid}
                        submitButtonText={JC_Utils.stringNullOrEmpty(currentProperty.Id) ? "Create Property" : "Update Property"}
                        onSubmit={handleSubmit}
                        isLoading={isLoading}
                        fields={[
                            // Address
                            {
                                inputId: "property-address-input",
                                type: FieldTypeEnum.Text,
                                label: "Address",
                                value: address,
                                onChange: (newValue) => setAddress(newValue as string),
                                validate: (v: any) => JC_Utils.stringNullOrEmpty(v) ? "Enter property address." : ""
                            },
                            // Building Type
                            {
                                inputId: "building-type-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "Building Type",
                                value: buildingTypeCode,
                                onChange: (newValue) => setBuildingTypeCode(newValue as string),
                                options: buildingTypeOptions,
                                enableSearch: true
                            },
                            // Company or Strata Title
                            {
                                inputId: "company-strata-input",
                                type: FieldTypeEnum.Text,
                                label: "Company or Strata Title",
                                value: companyStrataTitle,
                                onChange: (newValue) => setCompanyStrataTitle(newValue as string)
                            },
                            // No. of Bedrooms
                            {
                                inputId: "num-bedrooms-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "No. of Bedrooms",
                                value: numBedroomsCode,
                                onChange: (newValue) => setNumBedroomsCode(newValue as string),
                                options: numBedroomsOptions,
                                enableSearch: true
                            },
                            // Orientation
                            {
                                inputId: "orientation-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "Orientation",
                                value: orientationCode,
                                onChange: (newValue) => setOrientationCode(newValue as string),
                                options: orientationOptions,
                                enableSearch: true
                            },
                            // Storeys
                            {
                                inputId: "storeys-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "Storeys",
                                value: storeysCode,
                                onChange: (newValue) => setStoreysCode(newValue as string),
                                options: storeysOptions,
                                enableSearch: true
                            },
                            // Furnished
                            {
                                inputId: "furnished-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "Furnished",
                                value: furnishedCode,
                                onChange: (newValue) => setFurnishedCode(newValue as string),
                                options: furnishedOptions
                            },
                            // Occupied
                            {
                                inputId: "occupied-input",
                                type: FieldTypeEnum.Dropdown,
                                label: "Occupied",
                                value: occupiedCode,
                                onChange: (newValue) => setOccupiedCode(newValue as string),
                                options: occupiedOptions
                            }
                        ]}
                    />
                </div>
            </div>

            {/* Right Pane - Property Info */}
            <div className={styles.rightPane}>
                <div className={styles.rightPaneHeader}>
                    Property Information
                </div>
                <div className={styles.contentContainer}>
                    {!JC_Utils.stringNullOrEmpty(address) ? (
                        <div className={styles.propertyInfo}>
                            <div className={styles.infoSection}>
                                <div className={styles.sectionTitle}>Basic Information</div>
                                <div className={styles.infoGrid}>
                                    <div className={styles.infoLabel}>Address:</div>
                                    <div className={styles.infoValue}>{address}</div>

                                    <div className={styles.infoLabel}>Building Type:</div>
                                    <div className={styles.infoValue}>
                                        {buildingTypeOptions.find(o => o.OptionId === buildingTypeCode)?.Label || "Not specified"}
                                    </div>

                                    <div className={styles.infoLabel}>Company/Strata:</div>
                                    <div className={styles.infoValue}>{companyStrataTitle || "Not specified"}</div>

                                    <div className={styles.infoLabel}>Bedrooms:</div>
                                    <div className={styles.infoValue}>
                                        {numBedroomsOptions.find(o => o.OptionId === numBedroomsCode)?.Label || "Not specified"}
                                    </div>

                                    <div className={styles.infoLabel}>Orientation:</div>
                                    <div className={styles.infoValue}>
                                        {orientationOptions.find(o => o.OptionId === orientationCode)?.Label || "Not specified"}
                                    </div>

                                    <div className={styles.infoLabel}>Storeys:</div>
                                    <div className={styles.infoValue}>
                                        {storeysOptions.find(o => o.OptionId === storeysCode)?.Label || "Not specified"}
                                    </div>

                                    <div className={styles.infoLabel}>Furnished:</div>
                                    <div className={styles.infoValue}>
                                        {furnishedOptions.find(o => o.OptionId === furnishedCode)?.Label || "Not specified"}
                                    </div>

                                    <div className={styles.infoLabel}>Occupied:</div>
                                    <div className={styles.infoValue}>
                                        {occupiedOptions.find(o => o.OptionId === occupiedCode)?.Label || "Not specified"}
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className={styles.noProperty}>
                            Enter property details to see information
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}
