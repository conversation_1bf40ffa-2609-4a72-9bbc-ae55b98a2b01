@import '../../../global';

$height: 375px;



.dropdownPortal {
  position: fixed;
  max-height: $height;
  box-sizing: border-box;
  border-radius: 6px;
  z-index: 9999; /* Ensure dropdown appears above all other elements */
  animation: extendHeightAnimation 0.2s ease-in-out;
  padding: 4px 0;
  background-color: $offWhite;
  overflow-x: visible;

  // Container for the scrollable content
  .scrollContainer {
    @include hideScrollBar;
    max-height: $height;
    overflow-y: auto;
    position: relative;
    padding-bottom: 10px;
  }


}

.dropdownOption {
  position: relative;
  width: calc(100% + 4px);
  height: 40px;
  padding: 0 38px 0 10px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-content: flex-start;
  align-items: center;
  column-gap: 10px;
  border-radius: 6px;
  border: solid 2px $primaryColor;
  background-color: $offWhite;
  overflow: hidden;
  margin-bottom: 4px;
  margin-left: -2px;
  cursor: pointer;
  color: $offBlack;
  text-align: left;
  font-family: inherit;
  font-size: inherit;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;

  &:last-child {
    margin-bottom: 0;
  }

  &.selected {
    background-color: $greyHover;
    cursor: default;
    opacity: 0.7;
  }

  &:hover {
    background-color: $greyHover;
  }

  &:focus {
    outline: none;
    border-color: $primaryColor;
    box-shadow: 0 0 0 2px rgba(2, 255, 1, 0.3);
  }
}
