"use client"

import styles from "./page.module.scss";
import React from "react";
import { useEffect, useState } from "react";

import J<PERSON>_Spinner from "../components/JC_Spinner/JC_Spinner";
import JC_Title from "../components/JC_Title/JC_Title";
import J<PERSON>_Button from "../components/JC_Button/JC_Button";
import JC_Form from "../components/JC_Form/JC_Form";

import JC_ModalConfirmation from "../components/JC_ModalConfirmation/JC_ModalConfirmation";
import JC_Checkbox from "../components/JC_Checkbox/JC_Checkbox";
import { JC_Post } from "../apiServices/JC_Post";
import { signOut, useSession } from "next-auth/react";
import { D_FieldModel_Email, D_FieldModel_FirstName, D_FieldModel_LastName, D_FieldModel_Phone } from "../models/ComponentModels/JC_Field";
import { JC_ConfirmationModalUsageModel } from "../models/ComponentModels/JC_ConfirmationModalUsage";
import { UserModel } from "../models/User";
import { GlobalSettingsModel } from "../models/GlobalSettings";
import { FieldTypeEnum } from "../enums/FieldType";

import { JC_Utils } from "../Utils";


export default function Page_Account() {

    // - STATE - //

    const session = useSession();
    // Loading
    const [initialised, setInitialised] = useState<boolean>(false);
    const [saveLoading, setSaveLoading] = useState<boolean>(false);
    const [logoutLoading, setLogoutLoading] = useState<boolean>(false);
    // Account Details
    const [firstName, setFirstName] = useState<string>(session.data!.user.FirstName);
    const [lastName, setLastName] = useState<string>(session.data!.user.LastName);
    const [phone, setPhone] = useState<string>(session.data!.user.Phone ?? "");
    const [company, setCompany] = useState<string>(session.data!.user.CompanyName ?? "");
    const [emailPromotionsChecked, setEmailPromotionsChecked] = useState<boolean>(session.data!.user.IsEmailSubscribed);
    // Confirmation
    const [confirmationModalData, setConfirmationModalData] = useState<JC_ConfirmationModalUsageModel | null>();
    const [confirmationLoading, setConfirmationLoading] = useState<boolean>(false);


    // - INITIALISE - //

    useEffect(() => {
        // Check if details were just updated to show success message
        if (localStorage.getItem("showUpdatedAccountDetailsSuccess") == "1") {
            JC_Utils.showToastSuccess("Your details have been updated!")
            localStorage.setItem("showUpdatedAccountDetailsSuccess", "0");
        }

        // Mark as initialised
        setInitialised(true);
    }, []);


    // - HANDLES - //

    // Account Details
    async function saveAccountDetails() {
        setSaveLoading(true);
        let newUser:UserModel = new UserModel({
            ...session.data!.user,
            FirstName: firstName,
            LastName: lastName,
            Phone: !JC_Utils.stringNullOrEmpty(phone) ? phone : undefined,
            CompanyName: !JC_Utils.stringNullOrEmpty(company) ? company : undefined,
            IsEmailSubscribed: emailPromotionsChecked
        });
        // Update db User
        await JC_Post<UserModel>(UserModel.apiRoute, newUser);
        // Trigger "jwt()" callback to refresh User from db
        await JC_Post<GlobalSettingsModel>(GlobalSettingsModel.apiRoute, {
            Code: "ForceRefreshAuthToken",
            Description: "",
            Value: "1"
        });
        // Update the session with new data (need this plus the update in "jwt()" callback to get update showing properly)
        const newSession = session.data;
        newSession!.user = newUser as any;
        await session.update(newSession);
        // Show success toast after refresh
        localStorage.setItem("showUpdatedAccountDetailsSuccess", "1");
        // Refresh
        setTimeout(() => window.location.reload(), 100);
    }

    // Reset Password
    async function resetPassword() {
        setConfirmationModalData({
            width: "380px",
            title: "Reset Password Email",
            text: `We will send you a "Reset Password" link to your email. Continue?`,
            submitButtons: [{
                text: "Send Email",
                onSubmit: async () => {
                    setConfirmationLoading(true);
                    // Generate token and trigger email
                    await JC_Post("user/triggerResetPasswordToken", { email: session.data!.user.Email });
                    setConfirmationLoading(false);
                    setConfirmationModalData(null);
                    JC_Utils.showToastSuccess("A password reset link has been sent to your email!");
                }
            }]
        });
    }

    // - BUILD - //

    // Account Details
    function _buildAccountDetails() {
        return <div className={styles.accountDetailsContainer}>
            <JC_Form
                onSubmit={saveAccountDetails}
                isDisabled={confirmationLoading || logoutLoading}
                isLoading={saveLoading}
                fields={[
                    // Email
                    {
                        ...D_FieldModel_Email(),
                        overrideClass: styles.fieldOverride,
                        value: session.data!.user.Email,
                        readOnly: true
                    },
                    // First Name
                    {
                        ...D_FieldModel_FirstName(),
                        overrideClass: styles.fieldOverride,
                        value: firstName,
                        onChange: (newValue) => setFirstName(newValue),
                    },
                    // Last Name
                    {
                        ...D_FieldModel_LastName(),
                        overrideClass: styles.fieldOverride,
                        value: lastName,
                        onChange: (newValue) => setLastName(newValue),
                    },
                    // Phone
                    {
                        ...D_FieldModel_Phone(!JC_Utils.stringNullOrEmpty(phone)),
                        overrideClass: styles.fieldOverride,
                        value: phone,
                        onChange: (newValue) => setPhone(newValue)
                    },
                    // Company Name
                    {
                        inputId: "company-name-input",
                        type: FieldTypeEnum.Text,
                        label: "Company (optional)",
                        iconName: "User",
                        value: company,
                        onChange: (newValue) => setCompany(newValue)
                    }
                ]}
            />

            <hr style={{ width: "80%", borderTopWidth: "2px", borderColor: "grey" }}/>

            {/* Reset Password */}
            <JC_Button
                text="Reset Password"
                onClick={resetPassword}
                isDisabled={confirmationLoading || saveLoading || logoutLoading}
            />

            {/* Logout */}
            <JC_Button
                text="Logout"
                onClick={() => { setLogoutLoading(true); signOut({ callbackUrl: "/login" }); }}
                isDisabled={confirmationLoading || saveLoading}
                isLoading={logoutLoading}
            />
        </div>;
    }




    // - Main - //

    return !initialised
        ? (<JC_Spinner isPageBody />)
        : (
            <React.Fragment>

                {/* Main Container */}
                <div className={`${styles.mainContainer}`}>
                    <JC_Title title="Account Details" />
                    {_buildAccountDetails()}
                </div>

                {/* Confirmation */}
                {confirmationModalData &&
                <JC_ModalConfirmation
                    width={confirmationModalData.width}
                    title={confirmationModalData.title}
                    text={confirmationModalData.text}
                    isOpen={confirmationModalData != null}
                    onCancel={() => setConfirmationModalData(null)}
                    submitButtons={confirmationModalData.submitButtons}
                    isLoading={confirmationLoading}
                />}


            </React.Fragment>
        );
}
