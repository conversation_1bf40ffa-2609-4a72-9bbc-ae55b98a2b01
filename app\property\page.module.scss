@import '../global';

.mainContainer {
    display: flex;
    height: 100vh;
    width: 100%;
    background-color: $offWhite;

    // Left Pane - Property Form
    .leftPane {
        width: 400px;
        min-width: 400px;
        display: flex;
        flex-direction: column;

        .leftPaneHeader {
            padding: 20px;
            border-bottom: 1px solid $lightGrey;
            background-color: $primaryColor;
            color: $white;
            font-weight: bold;
            font-size: 16px;
        }

        .formContainer {
            flex: 1;
            padding: 30px;
            overflow-y: auto;

            .formTitle {
                font-size: 18px;
                font-weight: bold;
                color: $primaryColor;
                margin-bottom: 20px;
            }
        }
    }

    // Right Pane - Property Preview/Info
    .rightPane {
        flex: 1;
        display: flex;
        flex-direction: column;

        .rightPaneHeader {
            padding: 20px;
            border-bottom: 1px solid $lightGrey;
            background-color: $secondaryColor;
            color: $white;
            font-weight: bold;
            font-size: 16px;
        }

        .contentContainer {
            flex: 1;
            padding: 30px;
            overflow-y: auto;

            .propertyInfo {
                .infoSection {
                    margin-bottom: 25px;

                    .sectionTitle {
                        font-size: 16px;
                        font-weight: bold;
                        color: $primaryColor;
                        margin-bottom: 10px;
                        border-bottom: 1px solid $lightGrey;
                        padding-bottom: 5px;
                    }

                    .infoGrid {
                        display: grid;
                        grid-template-columns: 1fr 2fr;
                        gap: 10px;

                        .infoLabel {
                            font-weight: 500;
                            color: $darkGrey;
                        }

                        .infoValue {
                            color: $offBlack;
                        }
                    }

                    .roomsList {
                        display: flex;
                        flex-wrap: wrap;
                        gap: 8px;

                        .roomTag {
                            background-color: $primaryColor;
                            color: $primaryColor;
                            padding: 4px 8px;
                            border-radius: $tinyBorderRadius;
                            font-size: 12px;
                            font-weight: 500;
                        }
                    }
                }
            }
        }

        .noProperty {
            display: flex;
            align-items: center;
            justify-content: center;
            flex: 1;
            color: $darkGrey;
            font-style: italic;
            font-size: 16px;
        }
    }
}

// Multi-select dropdown styling
.multiSelectContainer {
    .selectedRooms {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        margin-top: 8px;

        .roomChip {
            background-color: $secondaryColor;
            color: $secondaryColor;
            padding: 4px 8px;
            border-radius: $tinyBorderRadius;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;

            .removeButton {
                cursor: pointer;
                font-weight: bold;

                &:hover {
                    color: $errorColor;
                }
            }
        }
    }
}

// Responsive Design
@media (max-width: $smallScreenSize) {
    .mainContainer {
        flex-direction: column;
        height: auto;

        .leftPane {
            width: 100%;
            min-width: auto;
            max-height: none;
        }

        .rightPane {
            min-height: 400px;
        }
    }
}
